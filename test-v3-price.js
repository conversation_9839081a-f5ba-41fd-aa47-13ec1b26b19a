#!/usr/bin/env node

const { ethers } = require('ethers');
require('dotenv').config();

const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************'
};

const FACTORY_V3_ABI = [
  'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
];

const POOL_V3_ABI = [
  'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function fee() view returns (uint24)'
];

async function main() {
  console.log('🔍 Testing Uniswap V3 Price Calculation\n');
  
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  const factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V3_FACTORY, FACTORY_V3_ABI, provider);
  
  // Test the 0.3% fee pool
  const poolAddress = await factory.getPool(SEPOLIA_ADDRESSES.WETH, SEPOLIA_ADDRESSES.USDC, 3000);
  console.log(`Pool Address: ${poolAddress}`);
  
  if (poolAddress === ethers.ZeroAddress) {
    console.log('❌ Pool does not exist');
    return;
  }
  
  const pool = new ethers.Contract(poolAddress, POOL_V3_ABI, provider);
  
  // Get pool data
  const [slot0, token0, token1, fee] = await Promise.all([
    pool.slot0(),
    pool.token0(),
    pool.token1(),
    pool.fee()
  ]);
  
  console.log(`Token0: ${token0}`);
  console.log(`Token1: ${token1}`);
  console.log(`Fee: ${fee} (${Number(fee)/10000}%)`);
  console.log(`SqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);
  console.log(`Tick: ${slot0.tick}`);
  
  // Determine which token is which
  const isToken0WETH = token0.toLowerCase() === SEPOLIA_ADDRESSES.WETH.toLowerCase();
  const isToken0USDC = token0.toLowerCase() === SEPOLIA_ADDRESSES.USDC.toLowerCase();
  
  console.log(`\nToken Ordering:`);
  console.log(`Token0 is WETH: ${isToken0WETH}`);
  console.log(`Token0 is USDC: ${isToken0USDC}`);
  
  // Calculate price using different methods
  const sqrtPriceX96 = BigInt(slot0.sqrtPriceX96.toString());
  const Q96 = BigInt(2) ** BigInt(96);
  
  console.log(`\n📊 Price Calculations:`);
  
  // Method 1: Direct calculation
  const numerator = sqrtPriceX96 * sqrtPriceX96;
  const denominator = Q96 * Q96;
  const price = Number(numerator) / Number(denominator);
  console.log(`Raw price (token1/token0): ${price}`);
  
  // Method 2: With decimal adjustment
  const decimalsAdjustment = isToken0WETH ? 10 ** (6 - 18) : 10 ** (18 - 6); // Adjust based on token ordering
  const adjustedPrice = price * decimalsAdjustment;
  console.log(`Decimal adjusted price: ${adjustedPrice}`);
  
  // Method 3: Calculate USDC per WETH regardless of token ordering
  let usdcPerWeth;
  if (isToken0WETH) {
    // token0 = WETH, token1 = USDC
    // price = USDC/WETH in raw units, need to adjust for decimals
    usdcPerWeth = price * (10 ** (6 - 18)); // USDC has 6 decimals, WETH has 18
  } else {
    // token0 = USDC, token1 = WETH
    // price = WETH/USDC in raw units
    // To get USDC/WETH: invert and adjust for decimals
    // 1 WETH = price USDC (in raw units)
    // 1 WETH (10^18 wei) = price USDC (10^6 units)
    // So 1 WETH = price * 10^6 / 10^18 USDC = price / 10^12 USDC
    usdcPerWeth = 1 / (price / (10 ** 12)); // Correct decimal adjustment
  }

  console.log(`USDC per WETH (method 3): ${usdcPerWeth}`);
  
  // Method 4: Using tick to calculate price
  const tickPrice = 1.0001 ** Number(slot0.tick);
  console.log(`Price from tick: ${tickPrice}`);
  
  // Method 5: Tick-based calculation (more reliable)
  console.log(`\n🎯 Tick-based calculation:`);

  // The tick represents log_1.0001(price) where price = token1/token0
  // So price = 1.0001^tick
  const tickBasedPrice = 1.0001 ** Number(slot0.tick);
  console.log(`Tick-based price (WETH/USDC in raw units): ${tickBasedPrice}`);

  // Since token0=USDC(6), token1=WETH(18):
  // tickBasedPrice = WETH_wei / USDC_units
  // To get USDC per WETH: invert and adjust decimals
  // 1 WETH (10^18 wei) costs how many USDC (10^6 units)?
  // USDC_units = WETH_wei / tickBasedPrice
  // USDC = USDC_units / 10^6, WETH = WETH_wei / 10^18
  // So USDC per WETH = (USDC_units / 10^6) / (WETH_wei / 10^18) = (USDC_units * 10^18) / (WETH_wei * 10^6)
  // But USDC_units = WETH_wei / tickBasedPrice
  // So USDC per WETH = (WETH_wei / tickBasedPrice * 10^18) / (WETH_wei * 10^6) = 10^12 / tickBasedPrice

  const usdcPerWethFromTick = (10 ** 12) / tickBasedPrice;
  console.log(`USDC per WETH from tick: ${usdcPerWethFromTick}`);

  const finalPrice = usdcPerWethFromTick;
  
  // Compare with V2 price
  console.log(`\n📈 Expected V2 price: ~471.65 USDC/WETH`);
  console.log(`V3 calculated price: ${finalPrice.toFixed(2)} USDC/WETH`);
  
  if (Math.abs(finalPrice - 471.65) < 100) {
    console.log('✅ V3 price calculation looks correct!');
  } else {
    console.log('❌ V3 price calculation is still wrong');
  }
}

main().catch(console.error);
