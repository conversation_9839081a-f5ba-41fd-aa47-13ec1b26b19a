#!/usr/bin/env node

const { ethers } = require('ethers');
require('dotenv').config();

const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************'
};

const FACTORY_V3_ABI = [
  'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
];

const POOL_V3_ABI = [
  'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function fee() view returns (uint24)',
  'function liquidity() view returns (uint128)'
];

async function main() {
  console.log('🔍 Debugging V3 Tick Calculation\n');
  
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  const factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V3_FACTORY, FACTORY_V3_ABI, provider);
  
  // Get V3 pool (0.3% fee)
  const poolAddress = await factory.getPool(SEPOLIA_ADDRESSES.WETH, SEPOLIA_ADDRESSES.USDC, 3000);
  console.log(`V3 Pool Address: ${poolAddress}`);
  
  if (poolAddress === ethers.ZeroAddress) {
    console.log('❌ No V3 pool exists');
    return;
  }
  
  const pool = new ethers.Contract(poolAddress, POOL_V3_ABI, provider);
  
  // Get pool data
  const [slot0, token0, token1, fee, liquidity] = await Promise.all([
    pool.slot0(),
    pool.token0(),
    pool.token1(),
    pool.fee(),
    pool.liquidity()
  ]);
  
  console.log(`📊 Pool Data:`);
  console.log(`Token0: ${token0}`);
  console.log(`Token1: ${token1}`);
  console.log(`Fee: ${fee} (${Number(fee)/10000}%)`);
  console.log(`Liquidity: ${liquidity.toString()}`);
  console.log(`SqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);
  console.log(`Tick: ${slot0.tick.toString()}`);
  
  // Determine token ordering
  const isToken0WETH = token0.toLowerCase() === SEPOLIA_ADDRESSES.WETH.toLowerCase();
  const isToken0USDC = token0.toLowerCase() === SEPOLIA_ADDRESSES.USDC.toLowerCase();
  
  console.log(`\n🏷️  Token Identification:`);
  console.log(`Token0 is WETH: ${isToken0WETH}`);
  console.log(`Token0 is USDC: ${isToken0USDC}`);
  
  if (isToken0USDC) {
    console.log(`Token0 = USDC (6 decimals), Token1 = WETH (18 decimals)`);
  } else if (isToken0WETH) {
    console.log(`Token0 = WETH (18 decimals), Token1 = USDC (6 decimals)`);
  }
  
  // Analyze the tick value
  const tickNumber = Number(slot0.tick);
  console.log(`\n📈 Tick Analysis:`);
  console.log(`Tick: ${tickNumber}`);
  
  // Check if this tick is reasonable
  // For WETH/USDC around $471, the tick should be much lower
  // Let's calculate what tick should correspond to ~471 USDC/WETH
  
  if (isToken0USDC) {
    // token0 = USDC, token1 = WETH
    // We want price = WETH/USDC in raw units
    // 1 WETH (10^18 wei) = 471 USDC (471 * 10^6 units)
    // So price = (471 * 10^6) / 10^18 = 471 / 10^12 = 0.000000000471
    const expectedPrice = 471 / (10 ** 12);
    const expectedTick = Math.log(expectedPrice) / Math.log(1.0001);
    
    console.log(`Expected price for 471 USDC/WETH: ${expectedPrice}`);
    console.log(`Expected tick for this price: ${expectedTick.toFixed(0)}`);
    console.log(`Actual tick: ${tickNumber}`);
    console.log(`Tick difference: ${(tickNumber - expectedTick).toFixed(0)}`);
  } else if (isToken0WETH) {
    // token0 = WETH, token1 = USDC
    // We want price = USDC/WETH in raw units
    // 1 WETH (10^18 wei) = 471 USDC (471 * 10^6 units)
    // So price = (471 * 10^6) / 10^18 = 471 / 10^12 = 0.000000000471
    const expectedPrice = (471 * (10 ** 6)) / (10 ** 18);
    const expectedTick = Math.log(expectedPrice) / Math.log(1.0001);
    
    console.log(`Expected price for 471 USDC/WETH: ${expectedPrice}`);
    console.log(`Expected tick for this price: ${expectedTick.toFixed(0)}`);
    console.log(`Actual tick: ${tickNumber}`);
    console.log(`Tick difference: ${(tickNumber - expectedTick).toFixed(0)}`);
  }
  
  // Calculate current price from tick
  const currentPrice = Math.pow(1.0001, tickNumber);
  console.log(`\nCurrent price from tick: ${currentPrice}`);
  
  // Try to understand what this price means
  if (isToken0USDC) {
    // price = WETH/USDC in raw units
    // To get USDC/WETH: invert and adjust decimals
    const usdcPerWeth = (10 ** 12) / currentPrice;
    console.log(`USDC per WETH: ${usdcPerWeth.toFixed(2)}`);
    
    // This is way too high, so maybe the pool is not actively traded
    // or there's a different issue
    
    if (usdcPerWeth > 10000) {
      console.log(`⚠️  Price seems unrealistic - pool might be:`);
      console.log(`   - Not actively traded`);
      console.log(`   - Have very low liquidity`);
      console.log(`   - Be in an unusual state`);
      console.log(`   - Have a different token configuration than expected`);
    }
  }
  
  // Check liquidity
  console.log(`\n💧 Liquidity Analysis:`);
  const liquidityNumber = Number(liquidity.toString());
  console.log(`Liquidity: ${liquidityNumber}`);
  
  if (liquidityNumber < 1000000) {
    console.log(`⚠️  Very low liquidity - this might explain the unusual price`);
  }
  
  // Compare with sqrtPriceX96 calculation
  console.log(`\n🔬 SqrtPriceX96 Analysis:`);
  const sqrtPriceX96 = BigInt(slot0.sqrtPriceX96.toString());
  const Q96 = BigInt(2) ** BigInt(96);
  
  const sqrtPrice = Number(sqrtPriceX96) / Number(Q96);
  const priceFromSqrt = sqrtPrice ** 2;
  
  console.log(`Price from sqrtPriceX96: ${priceFromSqrt}`);
  console.log(`Price from tick: ${currentPrice}`);
  console.log(`Difference: ${Math.abs(priceFromSqrt - currentPrice)}`);
  
  if (Math.abs(priceFromSqrt - currentPrice) < 0.001) {
    console.log(`✅ Tick and sqrtPriceX96 calculations match`);
  } else {
    console.log(`❌ Tick and sqrtPriceX96 calculations don't match`);
  }
  
  // Conclusion
  console.log(`\n📋 Conclusion:`);
  console.log(`The V3 pool appears to have a very different price than V2.`);
  console.log(`This could be due to:`);
  console.log(`1. Low liquidity in the V3 pool`);
  console.log(`2. Different trading activity between V2 and V3`);
  console.log(`3. The V3 pool being in an unusual state`);
  console.log(`4. Arbitrage opportunities actually existing (but risky due to low liquidity)`);
  
  console.log(`\nFor MEV bot purposes, you might want to:`);
  console.log(`1. Use a minimum liquidity threshold to filter out low-liquidity pools`);
  console.log(`2. Add price sanity checks to avoid unrealistic arbitrage opportunities`);
  console.log(`3. Consider using only V2 pools for more reliable arbitrage`);
}

main().catch(console.error);
