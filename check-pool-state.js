#!/usr/bin/env node

const { ethers } = require('ethers');
require('dotenv').config();

const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************'
};

const FACTORY_V2_ABI = [
  'function getPair(address tokenA, address tokenB) view returns (address pair)'
];

const PAIR_ABI = [
  'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function totalSupply() view returns (uint256)'
];

async function main() {
  console.log('🔍 Checking Pool State\n');
  
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  const factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V2_FACTORY, FACTORY_V2_ABI, provider);
  
  // Get V2 pool
  const pairAddress = await factory.getPair(SEPOLIA_ADDRESSES.WETH, SEPOLIA_ADDRESSES.USDC);
  console.log(`V2 Pair Address: ${pairAddress}`);
  
  if (pairAddress === ethers.ZeroAddress) {
    console.log('❌ No V2 pair exists');
    return;
  }
  
  const pair = new ethers.Contract(pairAddress, PAIR_ABI, provider);
  
  // Get pool data
  const [reserves, token0, token1, totalSupply] = await Promise.all([
    pair.getReserves(),
    pair.token0(),
    pair.token1(),
    pair.totalSupply()
  ]);
  
  console.log(`\n📊 Pool Data:`);
  console.log(`Token0: ${token0}`);
  console.log(`Token1: ${token1}`);
  console.log(`Reserve0: ${reserves[0].toString()}`);
  console.log(`Reserve1: ${reserves[1].toString()}`);
  console.log(`Total Supply: ${totalSupply.toString()}`);
  
  // Determine which token is which
  const isToken0WETH = token0.toLowerCase() === SEPOLIA_ADDRESSES.WETH.toLowerCase();
  const isToken0USDC = token0.toLowerCase() === SEPOLIA_ADDRESSES.USDC.toLowerCase();
  
  console.log(`\n🏷️  Token Identification:`);
  console.log(`Token0 is WETH: ${isToken0WETH}`);
  console.log(`Token0 is USDC: ${isToken0USDC}`);
  
  if (isToken0WETH) {
    console.log(`Token0 = WETH, Token1 = USDC`);
    console.log(`WETH Reserve: ${ethers.formatEther(reserves[0])} WETH`);
    console.log(`USDC Reserve: ${ethers.formatUnits(reserves[1], 6)} USDC`);
    
    const wethAmount = Number(ethers.formatEther(reserves[0]));
    const usdcAmount = Number(ethers.formatUnits(reserves[1], 6));
    const price = usdcAmount / wethAmount;
    console.log(`Price: ${price.toFixed(2)} USDC/WETH`);
  } else if (isToken0USDC) {
    console.log(`Token0 = USDC, Token1 = WETH`);
    console.log(`USDC Reserve: ${ethers.formatUnits(reserves[0], 6)} USDC`);
    console.log(`WETH Reserve: ${ethers.formatEther(reserves[1])} WETH`);
    
    const usdcAmount = Number(ethers.formatUnits(reserves[0], 6));
    const wethAmount = Number(ethers.formatEther(reserves[1]));
    const price = usdcAmount / wethAmount;
    console.log(`Price: ${price.toFixed(2)} USDC/WETH`);
  } else {
    console.log('❌ Unknown token configuration');
  }
  
  // Check if reserves are reasonable
  const reserve0Num = Number(reserves[0].toString());
  const reserve1Num = Number(reserves[1].toString());
  
  console.log(`\n🔍 Reserve Analysis:`);
  console.log(`Reserve0 raw: ${reserve0Num}`);
  console.log(`Reserve1 raw: ${reserve1Num}`);
  
  if (reserve0Num < 1000000 || reserve1Num < 1000000) {
    console.log('⚠️  Very low reserves detected - pool might be empty or have minimal liquidity');
  }
  
  if (reserve0Num === 0 || reserve1Num === 0) {
    console.log('❌ One or both reserves are zero - pool is empty');
  }
}

main().catch(console.error);
